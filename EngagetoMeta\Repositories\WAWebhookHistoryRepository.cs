using EngagetoMeta.DatabaseContext;
using EngagetoMeta.Entities;
using Newtonsoft.Json;
using System.Text.Json;

namespace EngagetoMeta.Repositories
{
    public class WAWebhookHistoryRepository : IWAWebhookHistoryRepository
    {
        private readonly ApplicationDbContext _applicationDbContext;

        public WAWebhookHistoryRepository(ApplicationDbContext applicationDbContext)
        {
            _applicationDbContext = applicationDbContext;
        }
        public async Task<bool> SaveWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, string? whatsAppMessageId, string? responseData, string? field, string? whatsAppStatus)
        {
            try
            {
                var webhookEntity = new WAWebhookHistoryEntities
                {
                    Id = Guid.NewGuid(),
                    WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
                    PhoneNumberID = phoneNumberID,
                    WhatsAppMessageId = whatsAppMessageId,
                    ResponseData = responseData,
                    Field = field,
                    WhatsAppStatus = whatsAppStatus,
                    CreatedAt = DateTime.UtcNow
                };

                await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public async Task<bool> SaveMessageWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, string? whatsAppMessageId, object? webhookData, string? status)
        {
            try
            {
                var responseData = webhookData is JsonElement ?
                    ((JsonElement)webhookData).GetRawText() :
                    JsonConvert.SerializeObject(webhookData);

                var webhookEntity = new WAWebhookHistoryEntities
                {
                    Id = Guid.NewGuid(),
                    WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
                    PhoneNumberID = phoneNumberID,
                    WhatsAppMessageId = whatsAppMessageId,
                    ResponseData = responseData,
                    Field = "messages",
                    WhatsAppStatus = status,
                    CreatedAt = DateTime.UtcNow
                };

                await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public async Task<bool> SaveTemplateStatusWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, object? webhookData, string? status)
        {
            try
            {
                var responseData = webhookData is JsonElement ?
                    ((JsonElement)webhookData).GetRawText() :
                    JsonConvert.SerializeObject(webhookData);

                var webhookEntity = new WAWebhookHistoryEntities
                {
                    Id = Guid.NewGuid(),
                    WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
                    PhoneNumberID = phoneNumberID,
                    WhatsAppMessageId = null, // No specific message ID for template status
                    ResponseData = responseData,
                    Field = "message_template_status_update",
                    WhatsAppStatus = status ?? "template_status_update",
                    CreatedAt = DateTime.UtcNow
                };

                await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<bool> SaveBusinessCapabilityWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, object? webhookData, long? messageLimit)
        {
            try
            {
                var responseData = webhookData is JsonElement ?
                    ((JsonElement)webhookData).GetRawText() :
                    JsonConvert.SerializeObject(webhookData);

                var webhookEntity = new WAWebhookHistoryEntities
                {
                    Id = Guid.NewGuid(),
                    WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
                    PhoneNumberID = phoneNumberID,
                    WhatsAppMessageId = null,
                    ResponseData = responseData,
                    Field = "business_capability_update",
                    WhatsAppStatus = $"message_limit_update_{messageLimit}",
                    CreatedAt = DateTime.UtcNow
                };

                await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public async Task<bool> SaveAccountAlertWebhookHistoryAsync(string? whatsAppBusinessAccountID, string? phoneNumberID, object? webhookData, string? alertType)
        {
            try
            {
                var responseData = webhookData is JsonElement ?
                    ((JsonElement)webhookData).GetRawText() :
                    JsonConvert.SerializeObject(webhookData);

                var webhookEntity = new WAWebhookHistoryEntities
                {
                    Id = Guid.NewGuid(),
                    WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
                    PhoneNumberID = phoneNumberID,
                    WhatsAppMessageId = null,
                    ResponseData = responseData,
                    Field = "account_alerts",
                    WhatsAppStatus = alertType ?? "account_alert",
                    CreatedAt = DateTime.UtcNow
                };

                await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
                var result = await _applicationDbContext.SaveChangesAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}
