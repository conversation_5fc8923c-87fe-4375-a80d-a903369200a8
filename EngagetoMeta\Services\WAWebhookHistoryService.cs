using EngagetoMeta.Repositories;
using EngagetoMeta.Dtos;
using Newtonsoft.Json;

namespace EngagetoMeta.Services
{
    public class WAWebhookHistoryService : IWAWebhookHistoryService
    {
        private readonly IWAWebhookHistoryRepository _waWebhookHistoryRepository;

        public WAWebhookHistoryService(IWAWebhookHistoryRepository waWebhookHistoryRepository)
        {
            _waWebhookHistoryRepository = waWebhookHistoryRepository;
        }

        /// <summary>
        /// Process and save webhook data based on field type (same pattern as LogHistoryEntitity)
        /// </summary>
        public async Task<bool> ProcessAndSaveWebhookAsync(WebhookDto webhookDto, string? field)
        {
            try
            {
                var entry = webhookDto.Entry?[0];
                var change = entry?.Changes?[0];
                var value = change?.Value;

                // Extract common data
                string? businessAccountId = value?.Metadata?.WhatsappBusinessAccountId ?? entry?.Id;
                string? phoneNumberId = value?.Metadata?.PhoneNumberId;

                // Process based on field type
                switch (field?.ToLower())
                {
                    case "messages":
                        return await ProcessMessagesWebhook(businessAccountId, phoneNumberId, value, webhookDto);

                    case "message_template_status_update":
                        return await _waWebhookHistoryRepository.SaveTemplateStatusWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, "template_status_update");

                    case "business_capability_update":
                        return await _waWebhookHistoryRepository.SaveBusinessCapabilityWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, value?.MessageLimitPerUser);

                    case "account_alerts":
                        return await _waWebhookHistoryRepository.SaveAccountAlertWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, "account_alert");

                    case "account_update":
                        return await _waWebhookHistoryRepository.SaveAccountAlertWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, webhookDto, "account_update");

                    default:
                        // Save generic webhook
                        return await _waWebhookHistoryRepository.SaveWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, null,
                            JsonConvert.SerializeObject(webhookDto),
                            field ?? "unknown", "generic_webhook");
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// Process messages webhook (handles both incoming messages and status updates)
        /// </summary>
        private async Task<bool> ProcessMessagesWebhook(string? businessAccountId, string? phoneNumberId, Value? value, WebhookDto webhookDto)
        {
            bool result = true;

            try
            {
                // Handle incoming messages
                if (value?.Messages != null && value.Messages.Any())
                {
                    foreach (var message in value.Messages)
                    {
                        var messageResult = await _waWebhookHistoryRepository.SaveMessageWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, message.Id, webhookDto, "received");

                        if (!messageResult) result = false;
                    }
                }

                // Handle message status updates
                if (value?.Statuses != null && value.Statuses.Any())
                {
                    foreach (var status in value.Statuses)
                    {
                        var statusResult = await _waWebhookHistoryRepository.SaveMessageWebhookHistoryAsync(
                            businessAccountId, phoneNumberId, status.Id, webhookDto, status.Status);

                        if (!statusResult) result = false;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// Save custom webhook history data
        /// </summary>
        public async Task<bool> SaveCustomWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            string? whatsAppMessageId,
            string? responseData,
            string? field,
            string? whatsAppStatus)
        {
            return await _waWebhookHistoryRepository.SaveWebhookHistoryAsync(
                whatsAppBusinessAccountID, phoneNumberID, whatsAppMessageId,
                responseData, field, whatsAppStatus);
        }

        /// <summary>
        /// Save message webhook history
        /// </summary>
        public async Task<bool> SaveMessageWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            string? whatsAppMessageId,
            object? webhookData,
            string? status)
        {
            return await _waWebhookHistoryRepository.SaveMessageWebhookHistoryAsync(
                whatsAppBusinessAccountID, phoneNumberID, whatsAppMessageId, webhookData, status);
        }

        /// <summary>
        /// Save template status webhook history
        /// </summary>
        public async Task<bool> SaveTemplateStatusWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            object? webhookData,
            string? status)
        {
            return await _waWebhookHistoryRepository.SaveTemplateStatusWebhookHistoryAsync(
                whatsAppBusinessAccountID, phoneNumberID, webhookData, status);
        }

        /// <summary>
        /// Save business capability webhook history
        /// </summary>
        public async Task<bool> SaveBusinessCapabilityWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            object? webhookData,
            long? messageLimit)
        {
            return await _waWebhookHistoryRepository.SaveBusinessCapabilityWebhookHistoryAsync(
                whatsAppBusinessAccountID, phoneNumberID, webhookData, messageLimit);
        }
    }
}
