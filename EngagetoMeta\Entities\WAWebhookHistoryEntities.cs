﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EngagetoMeta.Entities
{
    [Table("WAWebhookHistoryEntities")]
    public class WAWebhookHistoryEntities
    {
        [Key]
        public Guid Id { get; set; }
        public string? WhatsAppBusinessAccountID { get; set; }
        public string? PhoneNumberID { get; set; }
        public string? WhatsAppMessageId { set; get; }
        public string? ResponseData { get; set; }
        public string? Field { get; set; }
        public string? WhatsAppStatus { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public WAWebhookHistoryEntities() { }

        public WAWebhookHistoryEntities(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            string? whatsAppMessageId,
            string? responseData,
            string? field,
            string? whatsAppStatus)
        {
            Id = Guid.NewGuid();
            WhatsAppBusinessAccountID = whatsAppBusinessAccountID;
            PhoneNumberID = phoneNumberID;
            WhatsAppMessageId = whatsAppMessageId;
            ResponseData = responseData;
            Field = field;
            WhatsAppStatus = whatsAppStatus;
            CreatedAt = DateTime.UtcNow;
        }
    }
}
