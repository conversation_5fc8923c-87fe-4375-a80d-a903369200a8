# WAWebhookHistoryEntities Usage Example

## Overview

This shows how to save data to `WAWebhookHistoryEntities` table using the same pattern as `LogHistoryEntitity` - directly using `_genericRepository.SaveAsync()`.

## Usage Pattern (Same as LogHistoryEntitity)

### 1. Basic Usage - Direct Save

```csharp
// In any service where you have IGenericRepository injected
private readonly IGenericRepository _genericRepository;

// Save webhook history data
var webhookHistory = new WAWebhookHistoryEntities(
    whatsAppBusinessAccountID: "*********",
    phoneNumberID: "*********",
    whatsAppMessageId: "msg_123",
    responseData: JsonConvert.SerializeObject(webhookData),
    field: "messages",
    whatsAppStatus: "received"
);

var result = await _genericRepository.SaveAsync(webhookHistory);
if (result > 0)
{
    // Successfully saved
    _logger.LogInformation("Webhook history saved successfully");
}
```

### 2. Usage in Existing Services

```csharp
// Example: In any service that processes webhook data
public class YourWebhookService
{
    private readonly IGenericRepository _genericRepository;
    private readonly ILogger<YourWebhookService> _logger;

    public YourWebhookService(IGenericRepository genericRepository, ILogger<YourWebhookService> logger)
    {
        _genericRepository = genericRepository;
        _logger = logger;
    }

    public async Task ProcessWebhookAsync(WAWebhookDto webhookDto)
    {
        try
        {
            // Your existing webhook processing logic...
            
            // Save webhook history (same pattern as LogHistoryEntitity)
            await SaveWebhookHistoryAsync(webhookDto, "messages");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing webhook");
        }
    }

    private async Task SaveWebhookHistoryAsync(WAWebhookDto webhookDto, string field)
    {
        try
        {
            var entry = webhookDto.Entry?[0];
            var value = entry?.Changes?[0]?.Value;
            
            var webhookHistory = new WAWebhookHistoryEntities(
                value?.Metadata?.WhatsappBusinessAccountId,
                value?.Metadata?.PhoneNumberId,
                value?.Messages?.FirstOrDefault()?.Id,
                JsonConvert.SerializeObject(webhookDto),
                field,
                "received"
            );

            await _genericRepository.SaveAsync(webhookHistory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving webhook history");
        }
    }
}
```

### 3. Different Field Types Examples

```csharp
// For message webhooks
var messageWebhook = new WAWebhookHistoryEntities(
    businessAccountId,
    phoneNumberId,
    messageId,
    JsonConvert.SerializeObject(webhookDto),
    "messages",
    "received" // or "sent", "delivered", "read", "failed"
);
await _genericRepository.SaveAsync(messageWebhook);

// For template status updates
var templateWebhook = new WAWebhookHistoryEntities(
    businessAccountId,
    phoneNumberId,
    null, // No specific message ID
    JsonConvert.SerializeObject(webhookDto),
    "message_template_status_update",
    "template_status_update"
);
await _genericRepository.SaveAsync(templateWebhook);

// For business capability updates
var capabilityWebhook = new WAWebhookHistoryEntities(
    businessAccountId,
    phoneNumberId,
    null,
    JsonConvert.SerializeObject(webhookDto),
    "business_capability_update",
    $"message_limit_update_{messageLimit}"
);
await _genericRepository.SaveAsync(capabilityWebhook);
```

### 4. Querying Data (Using Generic Repository)

```csharp
// Get webhook history by field type
var messageWebhooks = await _genericRepository.GetByObjectAsync<WAWebhookHistoryEntities>(
    new Dictionary<string, object> { { "Field", "messages" } }
);

// Get webhook history by business account
var accountWebhooks = await _genericRepository.GetByObjectAsync<WAWebhookHistoryEntities>(
    new Dictionary<string, object> { { "WhatsAppBusinessAccountID", "*********" } }
);

// Get webhook history by message ID
var messageHistory = await _genericRepository.GetByObjectAsync<WAWebhookHistoryEntities>(
    new Dictionary<string, object> { { "WhatsAppMessageId", "msg_123" } }
);

// Get recent webhook history (last 24 hours)
var recentWebhooks = await _genericRepository.GetByObjectAsync<WAWebhookHistoryEntities>(
    new Dictionary<string, object> 
    { 
        { "CreatedAt >=", DateTime.UtcNow.AddDays(-1) },
        { "Field", "messages" }
    }
);
```

## Integration Points

### Where to Add Webhook History Saving

1. **WAWebhookMessageServiceAsync** (Already implemented)
   - Automatically saves all webhook data based on field type

2. **Template Processing Services**
   ```csharp
   // When processing template status updates
   await _genericRepository.SaveAsync(new WAWebhookHistoryEntities(
       businessAccountId, phoneNumberId, null, 
       JsonConvert.SerializeObject(templateData), 
       "message_template_status_update", "approved"
   ));
   ```

3. **Campaign Services**
   ```csharp
   // When sending campaign messages
   await _genericRepository.SaveAsync(new WAWebhookHistoryEntities(
       businessAccountId, phoneNumberId, messageId, 
       JsonConvert.SerializeObject(campaignData), 
       "campaign_message", "sent"
   ));
   ```

4. **Automation Services**
   ```csharp
   // When automation triggers
   await _genericRepository.SaveAsync(new WAWebhookHistoryEntities(
       businessAccountId, phoneNumberId, messageId, 
       JsonConvert.SerializeObject(automationData), 
       "automation_trigger", "executed"
   ));
   ```

## Field Types and Status Values

| Field Type | Common Status Values |
|------------|---------------------|
| `messages` | `received`, `sent`, `delivered`, `read`, `failed` |
| `message_template_status_update` | `approved`, `rejected`, `pending` |
| `business_capability_update` | `message_limit_update_{limit}` |
| `account_alerts` | `account_alert`, `quality_update` |
| `account_update` | `account_update`, `phone_number_update` |
| `campaign_message` | `sent`, `failed`, `scheduled` |
| `automation_trigger` | `executed`, `failed`, `skipped` |

## Best Practices

1. **Always use try-catch** when saving webhook history to avoid breaking main functionality
2. **Use background processing** for webhook history saving (like in the example)
3. **Include relevant context** in the ResponseData field as JSON
4. **Use descriptive field names** to categorize different webhook types
5. **Log success and failures** for debugging purposes
6. **Don't throw exceptions** from webhook history saving - log errors instead

## Database Table Structure

The table will be created with these columns:
- `Id` (UNIQUEIDENTIFIER, Primary Key)
- `WhatsAppBusinessAccountID` (NVARCHAR(MAX))
- `PhoneNumberID` (NVARCHAR(MAX))
- `WhatsAppMessageId` (NVARCHAR(MAX))
- `ResponseData` (NVARCHAR(MAX))
- `Field` (NVARCHAR(MAX))
- `WhatsAppStatus` (NVARCHAR(MAX))
- `CreatedAt` (DATETIME2, Default: GETUTCDATE())

This follows the exact same pattern as `LogHistoryEntities` table and uses the same `_genericRepository.SaveAsync()` method for consistency.
