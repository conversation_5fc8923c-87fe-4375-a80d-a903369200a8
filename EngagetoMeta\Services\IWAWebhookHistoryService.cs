using EngagetoMeta.Dtos;

namespace EngagetoMeta.Services
{
    public interface IWAWebhookHistoryService
    {
        /// <summary>
        /// Process and save webhook data based on field type (same pattern as LogHistoryEntitity)
        /// </summary>
        Task<bool> ProcessAndSaveWebhookAsync(WebhookDto webhookDto, string? field);

        Task<bool> SaveCustomWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            string? whatsAppMessageId,
            string? responseData,
            string? field,
            string? whatsAppStatus);

        /// <summary>
        /// Save message webhook history
        /// </summary>
        Task<bool> SaveMessageWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            string? whatsAppMessageId,
            object? webhookData,
            string? status);

        /// <summary>
        /// Save template status webhook history
        /// </summary>
        Task<bool> SaveTemplateStatusWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            object? webhookData,
            string? status);

        /// <summary>
        /// Save business capability webhook history
        /// </summary>
        Task<bool> SaveBusinessCapabilityWebhookHistoryAsync(
            string? whatsAppBusinessAccountID,
            string? phoneNumberID,
            object? webhookData,
            long? messageLimit);
    }
}
