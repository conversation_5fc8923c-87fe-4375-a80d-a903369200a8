# WAWebhookHistory Usage in Meta Project

## Overview

The WAWebhookHistory functionality has been implemented in the **EngagetoMeta** project using the **exact same pattern as LogHistoryEntitity**. This allows you to store webhook data based on field types directly in the meta project.

## Implementation Details

### Files Created/Modified in Meta Project:

1. **EngagetoMeta/Entities/WAWebhookHistoryEntities.cs** - Enhanced entity with constructor
2. **EngagetoMeta/IRepositories/IWAWebhookHistoryRepository.cs** - Repository interface
3. **EngagetoMeta/Repositories/WAWebhookHistoryRepository.cs** - Repository implementation
4. **EngagetoMeta/Services/IWAWebhookHistoryService.cs** - Service interface
5. **EngagetoMeta/Services/WAWebhookHistoryService.cs** - Service implementation
6. **EngagetoMeta/Controllers/WebhookController.cs** - Integrated webhook history saving
7. **EngagetoMeta/Dtos/WebhookDto.cs** - Added WhatsappBusinessAccountId property
8. **EngagetoMeta/DatabaseContext/ApplicationDbContext.cs** - EF configuration
9. **EngagetoMeta/Startup.cs** - DI registration

## How It Works (Same Pattern as LogHistoryEntitity)

### 1. Repository Pattern (Same as LogHistoryRepository)

```csharp
// In WAWebhookHistoryRepository.cs
public async Task<bool> SaveWebhookHistoryAsync(...)
{
    try
    {
        var webhookEntity = new WAWebhookHistoryEntities
        {
            Id = Guid.NewGuid(),
            WhatsAppBusinessAccountID = whatsAppBusinessAccountID,
            PhoneNumberID = phoneNumberID,
            WhatsAppMessageId = whatsAppMessageId,
            ResponseData = responseData,
            Field = field,
            WhatsAppStatus = whatsAppStatus,
            CreatedAt = DateTime.UtcNow
        };

        // Same pattern as LogHistoryEntitity
        await _applicationDbContext.WAWebhookHistoryEntities.AddAsync(webhookEntity);
        var result = await _applicationDbContext.SaveChangesAsync();
        return result > 0;
    }
    catch (Exception ex)
    {
        return false;
    }
}
```

### 2. Automatic Integration in WebhookController

The webhook history is automatically saved in the `WebhookController.ReceivedWAWebhookMessageAsync` method:

```csharp
// After Service Bus message is sent
await _serviceBusProducer.SendWebhookMessageAsync(webhookDto, receivedMessage, token);

// Save webhook history using the same pattern as LogHistoryEntitity
await _waWebhookHistoryService.ProcessAndSaveWebhookAsync(webhookDto, field);

// Continue with existing LogHistoryEntitity logging
await _logger.SaveSuccessLogHistoryAsyn(...);
```

## Field-Based Data Storage

The service automatically processes different webhook field types:

### Messages Field
```csharp
// For incoming messages
await _waWebhookHistoryService.SaveMessageWebhookHistoryAsync(
    businessAccountId, phoneNumberId, messageId, webhookData, "received");

// For message status updates  
await _waWebhookHistoryService.SaveMessageWebhookHistoryAsync(
    businessAccountId, phoneNumberId, messageId, webhookData, "delivered");
```

### Template Status Updates
```csharp
await _waWebhookHistoryService.SaveTemplateStatusWebhookHistoryAsync(
    businessAccountId, phoneNumberId, webhookData, "template_status_update");
```

### Business Capability Updates
```csharp
await _waWebhookHistoryService.SaveBusinessCapabilityWebhookHistoryAsync(
    businessAccountId, phoneNumberId, webhookData, messageLimit);
```

### Account Alerts
```csharp
await _waWebhookHistoryService.SaveAccountAlertWebhookHistoryAsync(
    businessAccountId, phoneNumberId, webhookData, "account_alert");
```

## Manual Usage in Other Meta Project Services

### 1. Inject the Service

```csharp
public class YourMetaService
{
    private readonly IWAWebhookHistoryService _waWebhookHistoryService;

    public YourMetaService(IWAWebhookHistoryService waWebhookHistoryService)
    {
        _waWebhookHistoryService = waWebhookHistoryService;
    }
}
```

### 2. Save Custom Webhook History

```csharp
// Save custom webhook data
await _waWebhookHistoryService.SaveCustomWebhookHistoryAsync(
    whatsAppBusinessAccountID: "*********",
    phoneNumberID: "*********",
    whatsAppMessageId: "msg_123",
    responseData: JsonConvert.SerializeObject(customData),
    field: "custom_field",
    whatsAppStatus: "custom_status"
);
```

### 3. Process Complete Webhook

```csharp
// Process and save webhook based on field type
await _waWebhookHistoryService.ProcessAndSaveWebhookAsync(webhookDto, field);
```

## Database Storage

Data is stored in the `WAWebhookHistoryEntities` table with these columns:

| Column | Type | Description |
|--------|------|-------------|
| `Id` | UNIQUEIDENTIFIER | Primary key |
| `WhatsAppBusinessAccountID` | NVARCHAR(MAX) | Business Account ID |
| `PhoneNumberID` | NVARCHAR(MAX) | Phone Number ID |
| `WhatsAppMessageId` | NVARCHAR(MAX) | Message ID (if applicable) |
| `ResponseData` | NVARCHAR(MAX) | Full webhook JSON data |
| `Field` | NVARCHAR(MAX) | Webhook field type |
| `WhatsAppStatus` | NVARCHAR(MAX) | Status/event type |
| `CreatedAt` | DATETIME2 | Timestamp (auto-generated) |

## Field Types and Status Values

| Field Type | Status Values |
|------------|---------------|
| `messages` | `received`, `sent`, `delivered`, `read`, `failed` |
| `message_template_status_update` | `template_status_update` |
| `business_capability_update` | `message_limit_update_{limit}` |
| `account_alerts` | `account_alert` |
| `account_update` | `account_update` |

## Benefits

1. **Same Pattern as LogHistoryEntitity**: Uses familiar repository and service patterns
2. **Automatic Processing**: Webhooks are automatically categorized and stored
3. **Meta Project Only**: All functionality contained within the meta project
4. **No External Dependencies**: Uses existing ApplicationDbContext and patterns
5. **Field-Based Organization**: Easy to query and analyze webhook data by type

## Current Integration

The webhook history saving is now **automatically integrated** into the existing webhook processing flow:

1. **Webhook Received** → WebhookController.ReceivedWAWebhookMessageAsync
2. **Service Bus Message Sent** → _serviceBusProducer.SendWebhookMessageAsync
3. **Webhook History Saved** → _waWebhookHistoryService.ProcessAndSaveWebhookAsync ✅ **NEW**
4. **Log History Saved** → _logger.SaveSuccessLogHistoryAsyn (existing)

This ensures that all webhook data is automatically stored based on field types without any changes needed to the EngagetoRepository namespace.

## Next Steps

1. **Database Migration**: Run EF migrations to create the WAWebhookHistoryEntities table
2. **Testing**: Test with different webhook field types
3. **Monitoring**: Monitor the webhook history data being stored
4. **Querying**: Use the repository methods to query stored webhook data

The implementation is ready and will start automatically storing webhook history data when webhooks are processed in the meta project!
